package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.client.dto.LegalReviewRequestDTO;
import com.whiskerguard.ai.client.dto.LegalReviewResponseDTO;
import com.whiskerguard.ai.service.legal.LegalModelService;
import com.whiskerguard.ai.service.legal.AsyncLegalReviewService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * REST controller for legal document review operations
 * 法律文档审查操作的REST控制器
 */
@RestController
@RequestMapping("/api/legal")
public class LegalReviewResource {

    private static final Logger log = LoggerFactory.getLogger(LegalReviewResource.class);

    private final LegalModelService legalModelService;
    private final AsyncLegalReviewService asyncLegalReviewService;

    public LegalReviewResource(LegalModelService legalModelService, AsyncLegalReviewService asyncLegalReviewService) {
        this.legalModelService = legalModelService;
        this.asyncLegalReviewService = asyncLegalReviewService;
    }

    /**
     * POST /api/legal/review : Review a legal document
     * 审查法律文档
     *
     * @param request the legal review request
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review")
    public ResponseEntity<LegalReviewResponseDTO> reviewDocument(@Valid @RequestBody LegalReviewRequestDTO request) {
        log.debug("REST request to review legal document of type: {}", request.getDocumentType());

        if (request.getDocumentContent() == null || request.getDocumentContent().trim().isEmpty()) {
            log.warn("Empty document content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during legal document review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * POST /api/legal/review/contract : Review a contract specifically
     * 专门审查合同
     *
     * @param contractContent the contract content to review
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review/contract")
    public ResponseEntity<LegalReviewResponseDTO> reviewContract(@RequestBody String contractContent) {
        log.debug("REST request to review contract");

        if (contractContent == null || contractContent.trim().isEmpty()) {
            log.warn("Empty contract content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewRequestDTO request = new LegalReviewRequestDTO();
            request.setDocumentContent(contractContent);
            request.setDocumentType(LegalReviewRequestDTO.DocumentType.CONTRACT);
            request.setPreferredModel(LegalReviewRequestDTO.LegalModelType.AUTO);

            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during contract review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * POST /api/legal/review/policy : Review a company policy specifically
     * 专门审查公司制度
     *
     * @param policyContent the company policy content to review
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review/policy")
    public ResponseEntity<LegalReviewResponseDTO> reviewPolicy(@RequestBody String policyContent) {
        log.debug("REST request to review company policy");

        if (policyContent == null || policyContent.trim().isEmpty()) {
            log.warn("Empty policy content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewRequestDTO request = new LegalReviewRequestDTO();
            request.setDocumentContent(policyContent);
            request.setDocumentType(LegalReviewRequestDTO.DocumentType.POLICY);
            request.setPreferredModel(LegalReviewRequestDTO.LegalModelType.AUTO);

            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during policy review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * GET /api/legal/models : Get available legal models
     * 获取可用的法律模型
     *
     * @return the ResponseEntity with status 200 (OK) and the list of available models in body
     */
    @GetMapping("/models")
    public ResponseEntity<List<String>> getAvailableModels() {
        log.debug("REST request to get available legal models");

        try {
            List<String> availableModels = legalModelService.getAvailableModels();
            return ResponseEntity.ok(availableModels);
        } catch (Exception e) {
            log.error("Error getting available models", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * GET /api/legal/health : Check health of legal models
     * 检查法律模型的健康状态
     *
     * @return the ResponseEntity with status 200 (OK) if models are healthy
     */
    @GetMapping("/health")
    public ResponseEntity<String> checkHealth() {
        log.debug("REST request to check legal models health");

        try {
            List<String> availableModels = legalModelService.getAvailableModels();
            if (availableModels.isEmpty()) {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body("No legal models are currently available");
            } else {
                return ResponseEntity.ok("Legal models are healthy. Available: " +
                    String.join(", ", availableModels));
            }
        } catch (Exception e) {
            log.error("Error checking legal models health", e);
            return ResponseEntity.internalServerError()
                .body("Error checking legal models health: " + e.getMessage());
        }
    }

    /**
     * POST /api/legal/review/batch : Review multiple legal documents
     * 批量审查多个法律文档
     *
     * @param requests the list of legal review requests
     * @return the ResponseEntity with status 200 (OK) and the list of review results in body
     */
    @PostMapping("/review/batch")
    public ResponseEntity<CompletableFuture<List<LegalReviewResponseDTO>>> reviewDocumentsBatch(
            @Valid @RequestBody List<LegalReviewRequestDTO> requests) {
        log.debug("REST request to review {} legal documents in batch", requests.size());

        if (requests == null || requests.isEmpty()) {
            log.warn("Empty batch request provided");
            return ResponseEntity.badRequest().build();
        }

        if (requests.size() > 10) {
            log.warn("Batch size too large: {}", requests.size());
            return ResponseEntity.badRequest().build();
        }

        try {
            CompletableFuture<List<LegalReviewResponseDTO>> result =
                asyncLegalReviewService.reviewDocumentsBatch(requests);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during batch legal document review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * POST /api/legal/review/async : Review a legal document asynchronously
     * 异步审查法律文档
     *
     * @param request the legal review request
     * @return the ResponseEntity with status 202 (Accepted) and the future result
     */
    @PostMapping("/review/async")
    public ResponseEntity<CompletableFuture<LegalReviewResponseDTO>> reviewDocumentAsync(
            @Valid @RequestBody LegalReviewRequestDTO request) {
        log.debug("REST request to review legal document asynchronously");

        if (request.getDocumentContent() == null || request.getDocumentContent().trim().isEmpty()) {
            log.warn("Empty document content provided for async review");
            return ResponseEntity.badRequest().build();
        }

        try {
            CompletableFuture<LegalReviewResponseDTO> result =
                asyncLegalReviewService.reviewDocumentAsync(request);
            return ResponseEntity.accepted().body(result);
        } catch (Exception e) {
            log.error("Error during async legal document review", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
