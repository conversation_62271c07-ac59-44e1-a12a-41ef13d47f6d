/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiInvocationResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI服务调用控制器，提供AI工具调用的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.AiStreamingService;
import com.whiskerguard.ai.service.invocation.FastAiInvocationService;
import com.whiskerguard.ai.service.invocation.DirectAiInvocationService;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * AI服务对外接口
 * <p>
 * 提供AI工具调用的REST API接口，支持多种AI模型的统一调用入口。
 * 该控制器负责接收客户端的AI调用请求，并将请求转发给相应的AI服务处理。
 * 所有AI调用都会被记录并可追踪，支持多租户隔离。
 */
@RestController
@RequestMapping("/api/ai")
public class AiInvocationResource {

    /**
     * AI调用服务，负责处理AI工具的实际调用逻辑
     */
    private final AiInvocationService aiInvocationService;

    /**
     * AI流式调用服务，负责处理流式输出
     */
    private final AiStreamingService aiStreamingService;

    /**
     * 快速AI调用服务，提供轻量级的AI调用方式
     */
    private final FastAiInvocationService fastAiInvocationService;

    /**
     * 直接AI调用服务，提供超高性能的AI调用方式
     */
    private final DirectAiInvocationService directAiInvocationService;

    /**
     * 构造函数，通过依赖注入初始化AI调用服务
     *
     * @param aiInvocationService AI调用服务实例
     * @param aiStreamingService AI流式调用服务实例
     * @param fastAiInvocationService 快速AI调用服务实例
     * @param directAiInvocationService 直接AI调用服务实例
     */
    public AiInvocationResource(AiInvocationService aiInvocationService, AiStreamingService aiStreamingService,
                               FastAiInvocationService fastAiInvocationService, DirectAiInvocationService directAiInvocationService) {
        this.aiInvocationService = aiInvocationService;
        this.aiStreamingService = aiStreamingService;
        this.fastAiInvocationService = fastAiInvocationService;
        this.directAiInvocationService = directAiInvocationService;
    }

    /**
     * 调用AI工具接口。
     * <p>
     * POST /api/ai/invoke : 使用指定参数调用AI工具。
     * 该接口接收包含工具类型、提示词和元数据的请求，并返回AI处理结果。
     * 所有调用都会被记录到数据库中，包括请求内容、响应结果和性能指标。
     *
     * @param dto AI调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果的ResponseEntity，状态码200(OK)，响应体为AiRequestDTO
     */
    @PostMapping("/invoke")
    public ResponseEntity<AiRequestDTO> invokeAI(@Valid @RequestBody AiInvocationRequestDTO dto) {
        AiRequestDTO result = aiInvocationService.invoke(dto);
        return ResponseEntity.ok(result);
    }

    /**
     * 流式调用AI工具接口。
     * <p>
     * POST /api/ai/stream : 使用指定参数流式调用AI工具。
     * 该接口接收包含工具类型、提示词和元数据的请求，并返回AI处理结果的流。
     * 响应会以Server-Sent Events (SSE)格式返回，前端可以实时显示生成的内容。
     * 所有调用都会被记录到数据库中，包括请求内容、响应结果和性能指标。
     *
     * @param dto AI流式调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果流的Flux，媒体类型为TEXT_EVENT_STREAM
     */
    @PostMapping(path = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<AiStreamResponseDTO> streamAI(@Valid @RequestBody AiStreamRequestDTO dto) {
        return aiStreamingService.streamInvoke(dto);
    }

    /**
     * 快速调用AI工具接口（性能优化版本）。
     * <p>
     * POST /api/ai/invoke-fast : 使用轻量级方式快速调用AI工具。
     * 该接口跳过 Temporal 工作流和数据库记录，直接调用AI模型以获得更快的响应时间。
     * 适用于对性能要求较高、不需要详细记录的场景。
     *
     * @param dto AI调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果的ResponseEntity，状态码200(OK)，响应体为AiRequestDTO
     */
    @PostMapping("/invoke-fast")
    public ResponseEntity<AiRequestDTO> invokeAIFast(@Valid @RequestBody AiInvocationRequestDTO dto) {
        AiRequestDTO result = fastAiInvocationService.fastInvoke(dto);
        return ResponseEntity.ok(result);
    }

    /**
     * 直接调用AI工具接口（超高性能版本）。
     * <p>
     * POST /api/ai/invoke-direct : 完全绕过所有中间层，直接调用AI API。
     * 该接口跳过所有可能的性能瓶颈：Temporal工作流、RAG增强、敏感词过滤、数据库记录等，
     * 直接与AI API通信，实现最快的响应时间。适用于对响应速度要求极高的场景。
     *
     * @param dto AI调用请求DTO，包含工具类型(toolKey)、提示词(prompt)
     * @return 包含AI响应结果的ResponseEntity，状态码200(OK)，响应体为AiRequestDTO
     */
    @PostMapping("/invoke-direct")
    public ResponseEntity<AiRequestDTO> invokeAIDirect(@Valid @RequestBody AiInvocationRequestDTO dto) {
        log.info("🚀 收到直接AI调用请求，工具: {}", dto.getToolKey());
        AiRequestDTO result = directAiInvocationService.directInvoke(dto);
        log.info("✅ 直接AI调用完成，响应长度: {}",
                result.getResponse() != null ? result.getResponse().length() : 0);
        return ResponseEntity.ok(result);
    }
}
