package com.whiskerguard.ai.service.legal;

import com.whiskerguard.ai.client.dto.LegalReviewRequestDTO;
import com.whiskerguard.ai.client.dto.LegalReviewResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Asynchronous legal document review service
 * 异步法律文档审查服务
 */
@Service
public class AsyncLegalReviewService {

    private static final Logger log = LoggerFactory.getLogger(AsyncLegalReviewService.class);

    private final LegalModelService legalModelService;

    public AsyncLegalReviewService(LegalModelService legalModelService) {
        this.legalModelService = legalModelService;
    }

    /**
     * Asynchronously review a legal document
     * 异步审查法律文档
     *
     * @param request The legal review request
     * @return CompletableFuture containing the review response
     */
    @Async("legalReviewTaskExecutor")
    public CompletableFuture<LegalReviewResponseDTO> reviewDocumentAsync(LegalReviewRequestDTO request) {
        log.debug("Starting async legal document review for document type: {}", request.getDocumentType());

        try {
            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            log.debug("Async legal document review completed successfully");
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("Error in async legal document review", e);
            CompletableFuture<LegalReviewResponseDTO> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * Asynchronously review multiple legal documents in batch
     * 异步批量审查多个法律文档
     *
     * @param requests List of legal review requests
     * @return CompletableFuture containing list of review responses
     */
    @Async("legalReviewTaskExecutor")
    public CompletableFuture<List<LegalReviewResponseDTO>> reviewDocumentsBatch(List<LegalReviewRequestDTO> requests) {
        log.debug("Starting async batch legal document review for {} documents", requests.size());

        try {
            // 并行处理多个文档
            List<CompletableFuture<LegalReviewResponseDTO>> futures = requests.stream()
                .map(this::reviewDocumentAsync)
                .collect(Collectors.toList());

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            return allFutures.thenApply(v -> 
                futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList())
            );

        } catch (Exception e) {
            log.error("Error in async batch legal document review", e);
            CompletableFuture<List<LegalReviewResponseDTO>> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * Check the status of async review operations
     * 检查异步审查操作的状态
     *
     * @return Current status information
     */
    public String getAsyncReviewStatus() {
        // 这里可以添加更详细的状态信息，比如队列长度、活跃任务数等
        return "Async legal review service is running";
    }
}
