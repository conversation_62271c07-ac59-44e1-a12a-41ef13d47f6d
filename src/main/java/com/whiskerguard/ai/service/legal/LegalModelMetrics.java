package com.whiskerguard.ai.service.legal;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Metrics collection for legal model operations
 * 法律模型操作的指标收集
 */
@Component
public class LegalModelMetrics {

    private final Counter reviewCounter;
    private final Counter successCounter;
    private final Counter errorCounter;
    private final Timer reviewTimer;
    private final AtomicLong activeReviews;

    public LegalModelMetrics(MeterRegistry meterRegistry) {
        // 总审查次数计数器
        this.reviewCounter = Counter.builder("legal.review.total")
            .description("Total number of legal document reviews")
            .register(meterRegistry);

        // 成功审查计数器
        this.successCounter = Counter.builder("legal.review.success")
            .description("Number of successful legal document reviews")
            .register(meterRegistry);

        // 错误计数器
        this.errorCounter = Counter.builder("legal.review.error")
            .description("Number of failed legal document reviews")
            .register(meterRegistry);

        // 审查处理时间计时器
        this.reviewTimer = Timer.builder("legal.review.duration")
            .description("Legal document review processing time")
            .register(meterRegistry);

        // 当前活跃审查数量
        this.activeReviews = new AtomicLong(0);
        Gauge.builder("legal.review.active")
            .description("Number of currently active legal reviews")
            .register(meterRegistry, this.activeReviews, AtomicLong::get);
    }

    /**
     * Record a review attempt
     * 记录一次审查尝试
     *
     * @param documentType The type of document being reviewed
     * @param modelUsed The AI model used for review
     */
    public void recordReviewAttempt(String documentType, String modelUsed) {
        reviewCounter.increment(
            "document.type", documentType,
            "model", modelUsed
        );
        activeReviews.incrementAndGet();
    }

    /**
     * Record a successful review
     * 记录一次成功的审查
     *
     * @param documentType The type of document reviewed
     * @param modelUsed The AI model used
     * @param duration The processing duration
     */
    public void recordReviewSuccess(String documentType, String modelUsed, Duration duration) {
        successCounter.increment(
            "document.type", documentType,
            "model", modelUsed
        );
        reviewTimer.record(duration);
        activeReviews.decrementAndGet();
    }

    /**
     * Record a failed review
     * 记录一次失败的审查
     *
     * @param documentType The type of document
     * @param modelUsed The AI model attempted
     * @param errorType The type of error encountered
     * @param duration The processing duration before failure
     */
    public void recordReviewError(String documentType, String modelUsed, String errorType, Duration duration) {
        errorCounter.increment(
            "document.type", documentType,
            "model", modelUsed,
            "error.type", errorType
        );
        reviewTimer.record(duration);
        activeReviews.decrementAndGet();
    }

    /**
     * Get current number of active reviews
     * 获取当前活跃审查数量
     *
     * @return Number of active reviews
     */
    public long getActiveReviewCount() {
        return activeReviews.get();
    }
}
