package com.whiskerguard.ai.service.legal;

import com.whiskerguard.ai.client.ChatLawApiClient;
import com.whiskerguard.ai.client.LaWGPTClient;
import com.whiskerguard.ai.client.dto.ChatLawResponseDTO;
import com.whiskerguard.ai.client.dto.ComplianceAssessmentDTO;
import com.whiskerguard.ai.client.dto.LegalIssueDTO;
import com.whiskerguard.ai.client.dto.LegalReviewRequestDTO;
import com.whiskerguard.ai.client.dto.LegalReviewResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Service for legal document review using multiple AI models
 * 使用多个AI模型进行法律文档审查的服务
 */
@Service
public class LegalModelService {

    private static final Logger log = LoggerFactory.getLogger(LegalModelService.class);

    private final ChatLawApiClient chatLawApiClient;
    private final LaWGPTClient laWGPTClient;

    public LegalModelService(ChatLawApiClient chatLawApiClient, LaWGPTClient laWGPTClient) {
        this.chatLawApiClient = chatLawApiClient;
        this.laWGPTClient = laWGPTClient;
    }

    /**
     * Perform legal document review with input validation
     * 执行法律文档审查，包含输入验证
     *
     * @param request Legal review request
     * @return Legal review response with analysis results
     */
    public LegalReviewResponseDTO reviewDocument(LegalReviewRequestDTO request) {
        log.info("Starting legal document review for document type: {}", request.getDocumentType());

        // 输入验证
        validateRequest(request);

        long startTime = System.currentTimeMillis();
        String reviewId = UUID.randomUUID().toString();

        try {
            // Determine which model to use
            // 确定使用哪个模型
            LegalReviewRequestDTO.LegalModelType modelToUse = determineModelToUse(request.getPreferredModel());

            // Perform the review based on document type and selected model
            // 根据文档类型和选择的模型执行审查
            String analysisResult = performAnalysis(request, modelToUse);

            // Parse and structure the analysis result
            // 解析并结构化分析结果
            LegalReviewResponseDTO response = parseAnalysisResult(analysisResult, modelToUse.getDisplayName());

            // Set metadata
            // 设置元数据
            response.setReviewId(reviewId);
            response.setProcessingTimeMs(System.currentTimeMillis() - startTime);

            log.info("Legal document review completed successfully. Review ID: {}, Processing time: {}ms",
                    reviewId, response.getProcessingTimeMs());

            return response;

        } catch (Exception e) {
            log.error("Error during legal document review. Review ID: {}", reviewId, e);

            // Return error response
            // 返回错误响应
            LegalReviewResponseDTO errorResponse = new LegalReviewResponseDTO();
            errorResponse.setReviewId(reviewId);
            errorResponse.setModelUsed("ERROR");
            errorResponse.setSummary("审查过程中发生错误: " + e.getMessage());
            errorResponse.setProcessingTimeMs(System.currentTimeMillis() - startTime);

            return errorResponse;
        }
    }

    /**
     * Determine which model to use based on preference and availability
     * 根据偏好和可用性确定使用哪个模型
     */
    private LegalReviewRequestDTO.LegalModelType determineModelToUse(LegalReviewRequestDTO.LegalModelType preferred) {
        if (preferred == LegalReviewRequestDTO.LegalModelType.CHATLAW) {
            if (chatLawApiClient.isConfigured()) {
                return LegalReviewRequestDTO.LegalModelType.CHATLAW;
            } else {
                log.warn("ChatLaw is preferred but not configured, falling back to LaWGPT");
                return LegalReviewRequestDTO.LegalModelType.LAWGPT;
            }
        } else if (preferred == LegalReviewRequestDTO.LegalModelType.LAWGPT) {
            if (laWGPTClient.isConfigured()) {
                return LegalReviewRequestDTO.LegalModelType.LAWGPT;
            } else {
                log.warn("LaWGPT is preferred but not configured, falling back to ChatLaw");
                return LegalReviewRequestDTO.LegalModelType.CHATLAW;
            }
        } else {
            // AUTO selection - prefer ChatLaw if available, otherwise LaWGPT
            // 自动选择 - 如果可用优先选择ChatLaw，否则选择LaWGPT
            if (chatLawApiClient.isConfigured()) {
                return LegalReviewRequestDTO.LegalModelType.CHATLAW;
            } else if (laWGPTClient.isConfigured()) {
                return LegalReviewRequestDTO.LegalModelType.LAWGPT;
            } else {
                throw new RuntimeException("No legal models are configured and available");
            }
        }
    }

    /**
     * Perform analysis using the selected model
     * 使用选择的模型执行分析
     */
    private String performAnalysis(LegalReviewRequestDTO request, LegalReviewRequestDTO.LegalModelType modelType) {
        switch (modelType) {
            case CHATLAW:
                return performChatLawAnalysis(request);
            case LAWGPT:
                return performLaWGPTAnalysis(request);
            default:
                throw new RuntimeException("Unsupported model type: " + modelType);
        }
    }

    /**
     * Perform analysis using ChatLaw
     * 使用ChatLaw执行分析
     */
    private String performChatLawAnalysis(LegalReviewRequestDTO request) {
        try {
            ChatLawResponseDTO response;

            switch (request.getDocumentType()) {
                case CONTRACT:
                    response = chatLawApiClient.reviewContract(request.getDocumentContent());
                    break;
                case POLICY:
                    response = chatLawApiClient.reviewCompanyPolicy(request.getDocumentContent());
                    break;
                default:
                    response = chatLawApiClient.queryLegalModel(
                        "请对以下文档进行法律审查：\n\n" + request.getDocumentContent()
                    );
                    break;
            }

            // Extract content from the first choice
            // 从第一个选择中提取内容
            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                return response.getChoices().get(0).getMessage().getContent();
            } else {
                throw new RuntimeException("Empty response from ChatLaw API");
            }

        } catch (Exception e) {
            log.error("Error calling ChatLaw API", e);
            throw new RuntimeException("ChatLaw analysis failed: " + e.getMessage(), e);
        }
    }

    /**
     * Perform analysis using LaWGPT
     * 使用LaWGPT执行分析
     */
    private String performLaWGPTAnalysis(LegalReviewRequestDTO request) {
        try {
            String response;

            switch (request.getDocumentType()) {
                case CONTRACT:
                    response = laWGPTClient.reviewContract(request.getDocumentContent());
                    break;
                case POLICY:
                    response = laWGPTClient.reviewCompanyPolicy(request.getDocumentContent());
                    break;
                default:
                    response = laWGPTClient.queryLegalModel(
                        "请对以下文档进行法律审查：\n\n" + request.getDocumentContent()
                    );
                    break;
            }

            return response;

        } catch (Exception e) {
            log.error("Error calling LaWGPT", e);
            throw new RuntimeException("LaWGPT analysis failed: " + e.getMessage(), e);
        }
    }

    /**
     * Parse the analysis result and create structured response
     * 解析分析结果并创建结构化响应
     */
    private LegalReviewResponseDTO parseAnalysisResult(String analysisResult, String modelUsed) {
        LegalReviewResponseDTO response = new LegalReviewResponseDTO();
        response.setModelUsed(modelUsed);
        response.setSummary(analysisResult);

        // Simple parsing logic - in a real implementation, this would be more sophisticated
        // 简单的解析逻辑 - 在实际实现中，这会更加复杂
        List<LegalIssueDTO> issues = extractIssuesFromText(analysisResult);
        List<String> suggestions = extractSuggestionsFromText(analysisResult);
        ComplianceAssessmentDTO compliance = assessCompliance(issues);

        response.setIssues(issues);
        response.setSuggestions(suggestions);
        response.setComplianceAssessment(compliance);

        return response;
    }

    /**
     * Extract legal issues from analysis text
     * 从分析文本中提取法律问题
     */
    private List<LegalIssueDTO> extractIssuesFromText(String text) {
        List<LegalIssueDTO> issues = new ArrayList<>();

        // Simple keyword-based extraction - in practice, this would use NLP techniques
        // 简单的基于关键词的提取 - 实际中会使用NLP技术
        if (text.contains("风险") || text.contains("问题")) {
            LegalIssueDTO issue = new LegalIssueDTO();
            issue.setType(LegalIssueDTO.IssueType.COMPLIANCE_RISK);
            issue.setSeverity(LegalIssueDTO.SeverityLevel.MEDIUM);
            issue.setDescription("检测到潜在的法律风险或问题");
            issues.add(issue);
        }

        if (text.contains("不合规") || text.contains("违反")) {
            LegalIssueDTO issue = new LegalIssueDTO();
            issue.setType(LegalIssueDTO.IssueType.REGULATORY_VIOLATION);
            issue.setSeverity(LegalIssueDTO.SeverityLevel.HIGH);
            issue.setDescription("检测到可能的合规违规");
            issues.add(issue);
        }

        return issues;
    }

    /**
     * Extract suggestions from analysis text
     * 从分析文本中提取建议
     */
    private List<String> extractSuggestionsFromText(String text) {
        List<String> suggestions = new ArrayList<>();

        // Simple extraction logic
        // 简单的提取逻辑
        if (text.contains("建议")) {
            suggestions.add("请参考AI模型提供的详细建议");
        }

        suggestions.add("建议咨询专业法律顾问进行进一步审查");

        return suggestions;
    }

    /**
     * Assess compliance based on identified issues
     * 根据识别的问题评估合规性
     */
    private ComplianceAssessmentDTO assessCompliance(List<LegalIssueDTO> issues) {
        ComplianceAssessmentDTO assessment = new ComplianceAssessmentDTO();

        int criticalCount = 0;
        int highCount = 0;
        int mediumCount = 0;
        int lowCount = 0;

        for (LegalIssueDTO issue : issues) {
            switch (issue.getSeverity()) {
                case CRITICAL:
                    criticalCount++;
                    break;
                case HIGH:
                    highCount++;
                    break;
                case MEDIUM:
                    mediumCount++;
                    break;
                case LOW:
                    lowCount++;
                    break;
            }
        }

        assessment.setCriticalIssues(criticalCount);
        assessment.setHighPriorityIssues(highCount);
        assessment.setMediumPriorityIssues(mediumCount);
        assessment.setLowPriorityIssues(lowCount);

        // Calculate overall score and status
        // 计算总体评分和状态
        int totalIssues = criticalCount + highCount + mediumCount + lowCount;
        if (criticalCount > 0) {
            assessment.setOverallScore(30);
            assessment.setStatus(ComplianceAssessmentDTO.ComplianceStatus.NON_COMPLIANT);
        } else if (highCount > 0) {
            assessment.setOverallScore(60);
            assessment.setStatus(ComplianceAssessmentDTO.ComplianceStatus.PARTIALLY_COMPLIANT);
        } else if (totalIssues > 0) {
            assessment.setOverallScore(80);
            assessment.setStatus(ComplianceAssessmentDTO.ComplianceStatus.PARTIALLY_COMPLIANT);
        } else {
            assessment.setOverallScore(95);
            assessment.setStatus(ComplianceAssessmentDTO.ComplianceStatus.COMPLIANT);
        }

        assessment.setNotes("基于AI模型分析的初步评估，建议进行人工复核");

        return assessment;
    }

    /**
     * Get available models status
     * 获取可用模型状态
     */
    public List<String> getAvailableModels() {
        List<String> availableModels = new ArrayList<>();

        if (chatLawApiClient.isConfigured()) {
            availableModels.add("ChatLaw (302.ai API)");
        }

        if (laWGPTClient.isConfigured()) {
            availableModels.add("LaWGPT (Private Deployment)");
        }

        return availableModels;
    }

    /**
     * Validate the legal review request
     * 验证法律审查请求
     *
     * @param request The request to validate
     * @throws IllegalArgumentException if validation fails
     */
    private void validateRequest(LegalReviewRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }

        if (request.getDocumentContent() == null || request.getDocumentContent().trim().isEmpty()) {
            throw new IllegalArgumentException("文档内容不能为空");
        }

        if (request.getDocumentContent().length() > 50000) {
            throw new IllegalArgumentException("文档内容过长，最大支持50,000字符");
        }

        if (request.getDocumentType() == null) {
            throw new IllegalArgumentException("文档类型不能为空");
        }

        // 检查是否包含潜在的恶意内容
        if (containsMaliciousContent(request.getDocumentContent())) {
            throw new SecurityException("文档内容包含潜在的恶意代码");
        }
    }

    /**
     * Check for potentially malicious content
     * 检查潜在的恶意内容
     *
     * @param content The content to check
     * @return true if malicious content is detected
     */
    private boolean containsMaliciousContent(String content) {
        String lowerContent = content.toLowerCase();
        String[] maliciousPatterns = {
            "<script", "javascript:", "data:", "vbscript:",
            "onload=", "onerror=", "onclick=", "eval("
        };

        for (String pattern : maliciousPatterns) {
            if (lowerContent.contains(pattern)) {
                return true;
            }
        }
        return false;
    }
}
