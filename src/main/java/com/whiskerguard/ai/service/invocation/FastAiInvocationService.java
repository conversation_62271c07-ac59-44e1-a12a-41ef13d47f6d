package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.exception.AiInvocationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 快速 AI 调用服务
 * <p>
 * 提供轻量级的 AI 调用方式，跳过 Temporal 工作流和复杂的处理流程，
 * 直接调用 AI 模型以获得更快的响应时间。适用于对性能要求较高的场景。
 */
@Service
public class FastAiInvocationService {

    private static final Logger log = LoggerFactory.getLogger(FastAiInvocationService.class);

    private final AiToolRepository toolRepo;
    private final AiToolRouter router;

    public FastAiInvocationService(AiToolRepository toolRepo, AiToolRouter router) {
        this.toolRepo = toolRepo;
        this.router = router;
    }

    /**
     * 快速 AI 调用，跳过工作流和数据库记录
     * <p>
     * 直接调用 AI 模型，不进行数据库记录和复杂的处理流程，
     * 以获得最快的响应时间。适用于实时性要求高的场景。
     *
     * @param dto AI 调用请求
     * @return AI 响应结果
     */
    public AiRequestDTO fastInvoke(AiInvocationRequestDTO dto) {
        long startTime = System.currentTimeMillis();
        log.info("开始快速AI调用，工具类型: {}", dto.getToolKey());

        try {
            // 1. 快速参数验证
            validateRequest(dto);

            // 2. 查找AI工具配置
            AiTool aiTool = findAiToolByKey(dto.getToolKey(), dto);

            // 3. 直接调用 AI 路由器，跳过工作流
            AiResult result = router.route(dto);

            // 4. 构建响应
            AiRequestDTO response = new AiRequestDTO();
            response.setToolType(dto.getToolKey());
            response.setPrompt(dto.getPrompt());
            response.setResponse(result.getContent());

            long duration = System.currentTimeMillis() - startTime;
            log.info("快速AI调用完成，耗时: {}ms", duration);
            return response;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("快速AI调用失败，耗时: {}ms, 错误: {}", duration, e.getMessage(), e);
            
            // 构建错误响应
            AiRequestDTO errorResponse = new AiRequestDTO();
            errorResponse.setToolType(dto.getToolKey());
            errorResponse.setPrompt(dto.getPrompt());
            errorResponse.setResponse("快速AI调用失败: " + e.getMessage());

            return errorResponse;
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(AiInvocationRequestDTO dto) {
        if (dto.getToolKey() == null || dto.getToolKey().trim().isEmpty()) {
            throw new IllegalArgumentException("工具类型不能为空");
        }
        if (dto.getPrompt() == null || dto.getPrompt().trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
    }

    /**
     * 查找AI工具配置
     */
    private AiTool findAiToolByKey(String toolKey, AiInvocationRequestDTO dto) {
        // 检查metadata中是否包含isModel字段
        boolean isModel = false;
        if (dto.getMetadata() != null && dto.getMetadata().containsKey("isModel")) {
            Object isModelValue = dto.getMetadata().get("isModel");
            if (isModelValue instanceof Boolean) {
                isModel = (Boolean) isModelValue;
            } else if (isModelValue instanceof String) {
                isModel = Boolean.parseBoolean((String) isModelValue);
            }
        }

        // 根据isModel值选择不同的查询方法
        if (isModel) {
            return toolRepo
                .findByToolKeyAndIsDeletedFalse(toolKey)
                .filter(tool -> tool.getIsModel() != null && tool.getIsModel())
                .orElseThrow(() -> new AiInvocationException("未找到模型配置: " + toolKey, toolKey, "MODEL_NOT_FOUND"));
        } else {
            return toolRepo
                .findByToolKeyAndIsDeletedFalseAndIsModelFalse(toolKey)
                .orElseThrow(() -> new AiInvocationException("未找到工具配置: " + toolKey, toolKey, "TOOL_NOT_FOUND"));
        }
    }
}
