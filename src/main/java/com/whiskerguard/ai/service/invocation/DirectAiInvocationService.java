package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.exception.AiInvocationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 直接 AI 调用服务 - 超高性能版本
 * <p>
 * 完全绕过所有中间层，直接调用 AI API，实现最快的响应时间。
 * 跳过：Temporal工作流、RAG增强、敏感词过滤、数据库记录等所有可能的性能瓶颈。
 */
@Service
public class DirectAiInvocationService {

    private static final Logger log = LoggerFactory.getLogger(DirectAiInvocationService.class);

    private final AiToolRepository toolRepo;
    private final WebClient.Builder webClientBuilder;

    public DirectAiInvocationService(AiToolRepository toolRepo, WebClient.Builder webClientBuilder) {
        this.toolRepo = toolRepo;
        this.webClientBuilder = webClientBuilder;
    }

    /**
     * 超快速直接 AI 调用
     * <p>
     * 完全绕过所有中间处理，直接调用 AI API，实现最快响应时间
     *
     * @param dto AI 调用请求
     * @return AI 响应结果
     */
    public AiRequestDTO directInvoke(AiInvocationRequestDTO dto) {
        long startTime = System.currentTimeMillis();
        log.info("开始直接AI调用，工具类型: {}", dto.getToolKey());

        try {
            // 1. 最简参数验证
            if (dto.getToolKey() == null || dto.getPrompt() == null) {
                throw new IllegalArgumentException("工具类型和提示词不能为空");
            }

            // 2. 快速查找AI工具配置
            AiTool aiTool = findAiToolByKeyFast(dto.getToolKey());
            log.debug("找到AI工具: {}, API URL: {}", aiTool.getName(), aiTool.getApiUrl());

            // 3. 直接调用 AI API，不经过任何中间层
            String response = callAiApiDirectly(aiTool, dto.getPrompt());

            // 4. 构建最简响应
            AiRequestDTO result = new AiRequestDTO();
            result.setToolType(dto.getToolKey());
            result.setPrompt(dto.getPrompt());
            result.setResponse(response);

            long duration = System.currentTimeMillis() - startTime;
            log.info("直接AI调用完成，耗时: {}ms", duration);
            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("直接AI调用失败，耗时: {}ms, 错误: {}", duration, e.getMessage(), e);
            
            AiRequestDTO errorResponse = new AiRequestDTO();
            errorResponse.setToolType(dto.getToolKey());
            errorResponse.setPrompt(dto.getPrompt());
            errorResponse.setResponse("直接AI调用失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 快速查找AI工具配置，使用缓存优化
     */
    private AiTool findAiToolByKeyFast(String toolKey) {
        return toolRepo
            .findByToolKeyAndIsDeletedFalseAndIsModelFalse(toolKey)
            .orElseThrow(() -> new AiInvocationException("未找到工具配置: " + toolKey, toolKey, "TOOL_NOT_FOUND"));
    }

    /**
     * 直接调用 AI API，不经过任何中间处理
     */
    private String callAiApiDirectly(AiTool aiTool, String prompt) {
        try {
            // 根据不同的AI工具类型，直接构建API请求
            String toolKey = aiTool.getToolKey().toLowerCase();
            
            if (toolKey.contains("doubao") || toolKey.contains("豆包")) {
                return callDouBaoDirectly(aiTool, prompt);
            } else if (toolKey.contains("kimi")) {
                return callKimiDirectly(aiTool, prompt);
            } else if (toolKey.contains("claude")) {
                return callClaudeDirectly(aiTool, prompt);
            } else if (toolKey.contains("deepseek")) {
                return callDeepSeekDirectly(aiTool, prompt);
            } else {
                // 默认使用通用OpenAI格式
                return callOpenAiFormatDirectly(aiTool, prompt);
            }
        } catch (Exception e) {
            log.error("直接调用AI API失败: {}", e.getMessage(), e);
            throw new RuntimeException("AI API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接调用豆包API
     */
    private String callDouBaoDirectly(AiTool aiTool, String prompt) {
        WebClient client = webClientBuilder
            .baseUrl(aiTool.getApiUrl())
            .defaultHeader("Authorization", aiTool.getApiKey())
            .build();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "doubao-lite");
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.7);

        return client.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))  // 30秒超时
            .map(response -> extractContentFromResponse(response))
            .block();
    }

    /**
     * 直接调用Kimi API
     */
    private String callKimiDirectly(AiTool aiTool, String prompt) {
        WebClient client = webClientBuilder
            .baseUrl(aiTool.getApiUrl())
            .defaultHeader("Authorization", "Bearer " + aiTool.getApiKey())
            .build();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "moonshot-v1-8k");
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.7);

        return client.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))
            .map(response -> extractContentFromResponse(response))
            .block();
    }

    /**
     * 直接调用Claude API
     */
    private String callClaudeDirectly(AiTool aiTool, String prompt) {
        WebClient client = webClientBuilder
            .baseUrl(aiTool.getApiUrl())
            .defaultHeader("x-api-key", aiTool.getApiKey())
            .defaultHeader("anthropic-version", "2023-06-01")
            .build();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "claude-3-haiku-20240307");
        requestBody.put("max_tokens", 2000);
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });

        return client.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))
            .map(response -> extractContentFromResponse(response))
            .block();
    }

    /**
     * 直接调用DeepSeek API
     */
    private String callDeepSeekDirectly(AiTool aiTool, String prompt) {
        // 构建完整的API URL
        String fullApiUrl = buildFullApiUrl(aiTool.getApiUrl(), "/v1/chat/completions");

        WebClient client = webClientBuilder
            .baseUrl(fullApiUrl)
            .defaultHeader("Authorization", "Bearer " + aiTool.getApiKey())
            .defaultHeader("Content-Type", "application/json")
            .build();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "deepseek-chat");
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.7);

        log.debug("调用DeepSeek API: {}", fullApiUrl);

        return client.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))
            .map(response -> extractContentFromResponse(response))
            .block();
    }

    /**
     * 使用通用OpenAI格式调用
     */
    private String callOpenAiFormatDirectly(AiTool aiTool, String prompt) {
        WebClient client = webClientBuilder
            .baseUrl(aiTool.getApiUrl())
            .defaultHeader("Authorization", "Bearer " + aiTool.getApiKey())
            .build();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "gpt-3.5-turbo");
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.7);

        return client.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(Map.class)
            .timeout(Duration.ofSeconds(30))
            .map(response -> extractContentFromResponse(response))
            .block();
    }

    /**
     * 从API响应中提取内容
     */
    @SuppressWarnings("unchecked")
    private String extractContentFromResponse(Map<String, Object> response) {
        try {
            if (response.containsKey("choices")) {
                Object choices = response.get("choices");
                if (choices instanceof java.util.List) {
                    java.util.List<Map<String, Object>> choicesList = (java.util.List<Map<String, Object>>) choices;
                    if (!choicesList.isEmpty()) {
                        Map<String, Object> firstChoice = choicesList.get(0);
                        if (firstChoice.containsKey("message")) {
                            Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
                            return (String) message.get("content");
                        }
                    }
                }
            }
            
            // Claude API 响应格式
            if (response.containsKey("content")) {
                Object content = response.get("content");
                if (content instanceof java.util.List) {
                    java.util.List<Map<String, Object>> contentList = (java.util.List<Map<String, Object>>) content;
                    if (!contentList.isEmpty()) {
                        return (String) contentList.get(0).get("text");
                    }
                }
            }
            
            return "AI响应格式解析失败: " + response.toString();
        } catch (Exception e) {
            log.error("解析AI响应失败: {}", e.getMessage(), e);
            return "AI响应解析失败: " + e.getMessage();
        }
    }
}
