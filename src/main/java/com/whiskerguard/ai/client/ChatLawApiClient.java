package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.ChatLawRequestDTO;
import com.whiskerguard.ai.client.dto.ChatLawResponseDTO;
import com.whiskerguard.ai.client.dto.ChatLawMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.util.Arrays;
import java.util.List;

/**
 * Client for ChatLaw API integration via 302.ai
 * 通过302.ai集成ChatLaw API的客户端
 */
@Component
public class ChatLawApiClient {

    private static final Logger log = LoggerFactory.getLogger(ChatLawApiClient.class);

    private final RestTemplate restTemplate;

    @Value("${whiskerguard.ai.chatlaw.api.url:https://api.302.ai/v1/chat/completions}")
    private String apiUrl;

    @Value("${whiskerguard.ai.chatlaw.api.key:}")
    private String apiKey;

    @Value("${whiskerguard.ai.chatlaw.model:ChatLaw}")
    private String defaultModel;

    @Value("${whiskerguard.ai.chatlaw.max-tokens:2000}")
    private Integer defaultMaxTokens;

    @Value("${whiskerguard.ai.chatlaw.temperature:0.7}")
    private Double defaultTemperature;

    public ChatLawApiClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * Send a legal query to ChatLaw API
     * 向ChatLaw API发送法律查询
     *
     * @param query The legal question or document content to analyze
     * @return ChatLaw API response
     */
    public ChatLawResponseDTO queryLegalModel(String query) {
        log.debug("Sending query to ChatLaw API: {}", query.substring(0, Math.min(query.length(), 100)) + "...");

        try {
            // Create message for the API request
            // 为API请求创建消息
            ChatLawMessageDTO userMessage = new ChatLawMessageDTO("user", query);
            List<ChatLawMessageDTO> messages = Arrays.asList(userMessage);

            // Create request DTO
            // 创建请求DTO
            ChatLawRequestDTO request = new ChatLawRequestDTO(messages);
            request.setModel(defaultModel);
            request.setMaxTokens(defaultMaxTokens);
            request.setTemperature(defaultTemperature);

            // Set up headers
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<ChatLawRequestDTO> entity = new HttpEntity<>(request, headers);

            // Make API call
            // 进行API调用
            ResponseEntity<ChatLawResponseDTO> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                entity,
                ChatLawResponseDTO.class
            );

            log.debug("ChatLaw API response received successfully");
            return response.getBody();

        } catch (HttpClientErrorException e) {
            log.error("Client error when calling ChatLaw API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("ChatLaw API client error: " + e.getMessage(), e);
        } catch (HttpServerErrorException e) {
            log.error("Server error when calling ChatLaw API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("ChatLaw API server error: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error when calling ChatLaw API", e);
            throw new RuntimeException("ChatLaw API call failed: " + e.getMessage(), e);
        }
    }

    /**
     * Send a contract review request to ChatLaw API
     * 向ChatLaw API发送合同审查请求
     *
     * @param contractContent The contract content to review
     * @return ChatLaw API response with contract analysis
     */
    public ChatLawResponseDTO reviewContract(String contractContent) {
        String prompt = "请对以下合同进行详细的法律审查，识别潜在的法律风险、不合规条款、模糊表述和缺失条款。请提供具体的改进建议：\n\n" + contractContent;
        return queryLegalModel(prompt);
    }

    /**
     * Send a company policy review request to ChatLaw API
     * 向ChatLaw API发送公司制度审查请求
     *
     * @param policyContent The company policy content to review
     * @return ChatLaw API response with policy analysis
     */
    public ChatLawResponseDTO reviewCompanyPolicy(String policyContent) {
        String prompt = "请对以下公司制度进行法律合规性审查，检查是否符合相关法律法规，识别可能的合规风险和改进建议：\n\n" + policyContent;
        return queryLegalModel(prompt);
    }

    /**
     * Check if the ChatLaw API client is properly configured
     * 检查ChatLaw API客户端是否正确配置
     *
     * @return true if configured, false otherwise
     */
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() &&
               apiUrl != null && !apiUrl.trim().isEmpty();
    }
}

