package com.whiskerguard.ai.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * DTO for ChatLaw message
 * 用于ChatLaw消息的数据传输对象
 */
public class ChatLawMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("role")
    private String role;

    @JsonProperty("content")
    private String content;

    public ChatLawMessageDTO() {}

    public ChatLawMessageDTO(String role, String content) {
        this.role = role;
        this.content = content;
    }

    // Getters and Setters
    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "ChatLawMessageDTO{" +
                "role='" + role + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}

