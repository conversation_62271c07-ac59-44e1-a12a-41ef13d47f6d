package com.whiskerguard.ai.client.dto;

import java.io.Serializable;

/**
 * Data Transfer Object for legal issues identified during document review
 * 文档审查过程中识别出的法律问题的数据传输对象
 */
public class LegalIssueDTO implements Serializable {

    private IssueType type;
    private SeverityLevel severity;
    private String description;

    public LegalIssueDTO() {
    }

    public IssueType getType() {
        return type;
    }

    public void setType(IssueType type) {
        this.type = type;
    }

    public SeverityLevel getSeverity() {
        return severity;
    }

    public void setSeverity(SeverityLevel severity) {
        this.severity = severity;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Types of legal issues that can be identified
     * 可以识别的法律问题类型
     */
    public enum IssueType {
        REGULATORY_VIOLATION,
        COMPLIANCE_RISK,
        CONTRACTUAL_AMBIGUITY,
        LEGAL_INCONSISTENCY,
        POLICY_GAP
    }

    /**
     * Severity levels for legal issues
     * 法律问题的严重程度级别
     */
    public enum SeverityLevel {
        CRITICAL,
        HIGH,
        MEDIUM,
        LOW
    }
}
