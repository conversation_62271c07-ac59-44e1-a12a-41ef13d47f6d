package com.whiskerguard.ai.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;

/**
 * DTO for ChatLaw API request
 * 用于ChatLaw API请求的数据传输对象
 */
public class ChatLawRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("model")
    private String model = "ChatLaw";

    @JsonProperty("messages")
    private List<ChatLawMessageDTO> messages;

    @JsonProperty("max_tokens")
    private Integer maxTokens = 2000;

    @JsonProperty("temperature")
    private Double temperature = 0.7;

    @JsonProperty("stream")
    private Boolean stream = false;

    public ChatLawRequestDTO() {}

    public ChatLawRequestDTO(List<ChatLawMessageDTO> messages) {
        this.messages = messages;
    }

    // Getters and Setters
    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatLawMessageDTO> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatLawMessageDTO> messages) {
        this.messages = messages;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    @Override
    public String toString() {
        return "ChatLawRequestDTO{" +
                "model='" + model + '\'' +
                ", messages=" + messages +
                ", maxTokens=" + maxTokens +
                ", temperature=" + temperature +
                ", stream=" + stream +
                '}';
    }
}

