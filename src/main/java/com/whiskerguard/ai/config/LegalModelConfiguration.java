package com.whiskerguard.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import java.util.concurrent.Executor;

/**
 * Configuration for AI legal models integration
 * AI法律模型集成配置
 */
@Configuration
@EnableAsync
public class LegalModelConfiguration {

    /**
     * Configure RestTemplate for AI model API calls
     * 为AI模型API调用配置RestTemplate
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        // Configure simple request factory with timeouts
        // 配置带超时的简单请求工厂
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000); // 30 seconds
        factory.setReadTimeout(60000);    // 60 seconds

        return builder
            .requestFactory(() -> factory)
            .build();
    }

    /**
     * Configure task executor for async legal review operations
     * 为异步法律审查操作配置任务执行器
     */
    @Bean("legalReviewTaskExecutor")
    public Executor legalReviewTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);           // 核心线程数
        executor.setMaxPoolSize(8);            // 最大线程数
        executor.setQueueCapacity(100);        // 队列容量
        executor.setThreadNamePrefix("LegalReview-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}
